#!/usr/bin/env python3
"""
快速中文TTS推理速度测试
简化版本，快速测试模型推理性能
"""

import os
import time
import torch
from melo.api import TTS

def main():
    print("=" * 50)
    print("MeloTTS 中文模型快速速度测试")
    print("=" * 50)
    
    # 模型路径
    ckpt_dir = 'ckpts/MeloTTS-Chinese'
    config_path = os.path.join(ckpt_dir, 'config.json')
    ckpt_path = os.path.join(ckpt_dir, 'checkpoint.pth')
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在: {config_path}")
        return
    if not os.path.exists(ckpt_path):
        print(f"错误: 模型文件不存在: {ckpt_path}")
        return
    
    print(f"加载模型从: {ckpt_dir}")
    
    # 加载模型
    try:
        model = TTS(language='ZH', device='auto', use_hf=False, 
                   config_path=config_path, ckpt_path=ckpt_path)
        print(f"✓ 模型加载成功，使用设备: {model.device}")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return
    
    # 获取说话人
    speaker_ids = model.hps.data.spk2id
    speakers = list(speaker_ids.keys())
    print(f"✓ 可用说话人: {speakers}")
    
    # 测试文本
    test_texts = [
        "你好世界",  # 短文本
        "我最近在学习machine learning，希望能够在未来的artificial intelligence领域有所建树。",  # 中等文本
        "人工智能技术的发展日新月异，从早期的专家系统到现在的深度学习和大语言模型，我们见证了计算机科学领域的巨大进步。机器学习算法能够从大量数据中学习模式，并在图像识别、自然语言处理、语音合成等领域取得了突破性成果。"  # 长文本
    ]
    
    text_labels = ["短文本", "中等文本", "长文本"]
    
    print("\n开始推理速度测试...")
    print("-" * 50)
    
    # 选择第一个说话人进行测试
    speaker = speakers[0]
    speaker_id = speaker_ids[speaker]
    print(f"使用说话人: {speaker}")
    
    # 预热
    print("预热模型...")
    model.tts_to_file("测试", speaker_id, "warmup.wav")
    if os.path.exists("warmup.wav"):
        os.remove("warmup.wav")
    
    # 测试每种文本长度
    for i, (text, label) in enumerate(zip(test_texts, text_labels)):
        print(f"\n测试 {label} (长度: {len(text)} 字符)")
        print(f"文本: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        times = []
        for run in range(3):  # 运行3次取平均
            start_time = time.time()
            output_path = f"test_output_{i}_{run}.wav"
            model.tts_to_file(text, speaker_id, output_path)
            end_time = time.time()
            
            inference_time = end_time - start_time
            times.append(inference_time)
            
            # 清理文件
            if os.path.exists(output_path):
                os.remove(output_path)
            
            print(f"  第{run+1}次: {inference_time:.3f}s")
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"  平均时间: {avg_time:.3f}s")
        print(f"  最快: {min_time:.3f}s, 最慢: {max_time:.3f}s")
        
        # 计算实时率 (RTF - Real Time Factor)
        # 假设音频时长约为文本长度 * 0.1秒 (粗略估计)
        estimated_audio_duration = len(text) * 0.1
        rtf = avg_time / estimated_audio_duration if estimated_audio_duration > 0 else 0
        print(f"  实时率 (RTF): {rtf:.2f} (越小越好，<1表示实时)")
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("说明: RTF < 1 表示可以实时合成")
    print("     RTF = 推理时间 / 音频时长")

if __name__ == "__main__":
    main()
