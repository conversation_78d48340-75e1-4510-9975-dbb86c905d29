#!/usr/bin/env python3
"""
快速开始示例：使用OpenAI TTS生成中英文混合数据并训练MeloTTS
"""

import os
import openai
from pathlib import Path
import soundfile as sf
import librosa
from tqdm import tqdm

def quick_generate_dataset(num_samples=50, output_dir="quick_dataset"):
    """快速生成小规模数据集用于测试"""
    
    # 检查API密钥
    if not os.getenv('OPENAI_API_KEY'):
        print("请设置 OPENAI_API_KEY 环境变量")
        print("export OPENAI_API_KEY='your_api_key_here'")
        return False
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    audio_dir = output_path / "wavs"
    audio_dir.mkdir(exist_ok=True)
    
    # 预定义的中英文混合文本
    mixed_texts = [
        "我最近在学习machine learning，希望能够在未来的artificial intelligence领域有所建树。",
        "今天下午，我们准备去shopping mall购物，然后晚上去看一场movie。",
        "这个project的design真的很不错，我觉得值得推荐给其他developer。",
        "我们公司正在使用cloud computing来提高workflow的效率。",
        "在tech conference上，我遇到了很多software engineer的专家。",
        "这款mobile app的user interface设计得很简洁。",
        "我们需要optimize这个algorithm，才能达到预期的performance。",
        "通过data analysis，我们可以更好地理解用户需求。",
        "这次hackathon的主题是artificial intelligence和blockchain技术。",
        "我正在research一个关于deep learning的innovative solution。",
        "这个startup的business model很有创意，值得关注。",
        "我们的team正在develop一个revolutionary的platform。",
        "这个framework可以帮助我们更efficient地处理big data。",
        "在innovation lab里，我们探索了很多cutting-edge的技术。",
        "这个open source project的community非常active。",
        "我们使用agile methodology来管理software development process。",
        "这个API的documentation写得很详细，很容易integration。",
        "我们的database需要进行optimization来提高query performance。",
        "这个machine learning model的accuracy已经达到了industry standard。",
        "我们正在planning一个关于digital transformation的workshop。"
    ]
    
    # 如果需要更多样本，重复使用文本
    if num_samples > len(mixed_texts):
        texts_to_use = (mixed_texts * (num_samples // len(mixed_texts) + 1))[:num_samples]
    else:
        texts_to_use = mixed_texts[:num_samples]
    
    print(f"开始生成 {num_samples} 条中英文混合语音数据...")
    
    client = openai.OpenAI()
    metadata_lines = []
    
    for i, text in enumerate(tqdm(texts_to_use, desc="生成语音")):
        try:
            # 使用OpenAI TTS生成语音
            response = client.audio.speech.create(
                model="tts-1-hd",
                voice="alloy",  # 可选: alloy, echo, fable, onyx, nova, shimmer
                input=text,
                response_format="wav"
            )
            
            # 保存音频文件
            audio_filename = f"{i:04d}.wav"
            audio_path = audio_dir / audio_filename
            
            # 写入音频数据
            with open(audio_path, 'wb') as f:
                f.write(response.content)
            
            # 处理音频确保格式正确
            audio, sr = librosa.load(audio_path, sr=44100, mono=True)
            audio = librosa.util.normalize(audio)
            sf.write(audio_path, audio, sr)
            
            # 添加到元数据
            relative_path = f"wavs/{audio_filename}"
            metadata_line = f"{relative_path}|target_speaker|ZH_MIX_EN|{text}"
            metadata_lines.append(metadata_line)
            
        except Exception as e:
            print(f"生成第 {i} 条数据时出错: {e}")
            continue
    
    # 保存元数据文件
    metadata_file = output_path / "metadata.list"
    with open(metadata_file, 'w', encoding='utf-8') as f:
        for line in metadata_lines:
            f.write(line + '\n')
    
    print(f"数据集生成完成！")
    print(f"- 生成了 {len(metadata_lines)} 条语音数据")
    print(f"- 音频文件保存在: {audio_dir}")
    print(f"- 元数据文件: {metadata_file}")
    
    return True

def prepare_for_training(dataset_dir="quick_dataset"):
    """准备训练数据"""
    
    dataset_path = Path(dataset_dir)
    if not dataset_path.exists():
        print(f"数据集目录不存在: {dataset_dir}")
        return False
    
    print("准备训练数据...")
    
    # 检查是否在melo目录下
    melo_dir = Path("melo")
    if not melo_dir.exists():
        print("请在MeloTTS项目根目录下运行此脚本")
        return False
    
    # 复制数据到melo/data目录
    target_dir = melo_dir / "data" / "quick_training"
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制元数据文件
    source_metadata = dataset_path / "metadata.list"
    target_metadata = target_dir / "metadata.list"
    
    import shutil
    shutil.copy2(source_metadata, target_metadata)
    
    # 创建音频文件的软链接
    target_audio_dir = target_dir / "wavs"
    if target_audio_dir.exists():
        shutil.rmtree(target_audio_dir)
    
    source_audio_dir = dataset_path / "wavs"
    target_audio_dir.symlink_to(source_audio_dir.absolute())
    
    print(f"训练数据已准备完成: {target_dir}")
    
    # 提供训练命令
    print("\n下一步：运行以下命令开始训练")
    print("cd melo")
    print(f"python preprocess_text.py --metadata data/quick_training/metadata.list")
    print("bash train.sh data/quick_training/config.json 1")
    
    return True

def main():
    """主函数"""
    print("=== MeloTTS 声音克隆快速开始 ===")
    print()
    
    # 检查API密钥
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 未设置 OPENAI_API_KEY")
        print("请先设置OpenAI API密钥：")
        print("export OPENAI_API_KEY='your_api_key_here'")
        print()
        print("获取API密钥：https://platform.openai.com/api-keys")
        return
    
    print("✅ OpenAI API密钥已设置")
    
    # 生成数据集
    print("\n步骤1: 生成中英文混合语音数据")
    success = quick_generate_dataset(num_samples=50, output_dir="quick_dataset")
    
    if not success:
        print("❌ 数据生成失败")
        return
    
    print("✅ 数据生成成功")
    
    # 准备训练
    print("\n步骤2: 准备训练数据")
    success = prepare_for_training("quick_dataset")
    
    if not success:
        print("❌ 训练准备失败")
        return
    
    print("✅ 训练准备完成")
    
    print("\n=== 快速开始完成 ===")
    print("现在可以开始训练MeloTTS模型了！")
    print()
    print("预计成本：约 $1-2 USD（50条样本）")
    print("训练时间：约 1-4 小时（取决于GPU性能）")
    print()
    print("注意事项：")
    print("1. 这是一个快速测试版本，实际应用建议使用更多数据")
    print("2. 可以添加少量真实语音数据来提高质量")
    print("3. 训练完成后在 melo/logs/ 目录查看模型文件")

if __name__ == "__main__":
    main()
