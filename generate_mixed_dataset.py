#!/usr/bin/env python3
"""
使用大模型生成中英文混合语料，用于训练MeloTTS声音克隆模型
"""

import os
import json
import time
import random
from pathlib import Path
from typing import List, Dict
# import openai
from tqdm import tqdm
import librosa
import soundfile as sf
import requests

class MixedDatasetGenerator:
    def __init__(self, output_dir: str = "generated_dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.audio_dir = self.output_dir / "wavs"
        self.audio_dir.mkdir(exist_ok=True)
        
        self.metadata_file = self.output_dir / "metadata.list"
        self.f5tts_url = "http://127.0.0.1:8080/f5tts/tts"
        
    def generate_mixed_texts(self, num_samples: int = 1000) -> List[str]:
        """生成中英文混合文本"""
        
        # 预定义的中英混合文本模板
        templates = [
            "我最近在学习{english_term}，希望能够在未来的{english_field}领域有所建树。",
            "今天下午，我们准备去{english_place}，然后晚上去看一场{english_activity}。",
            "这个{english_product}的{english_feature}真的很不错，我觉得值得推荐。",
            "我们公司正在使用{english_tech}来提高{english_process}的效率。",
            "在{english_event}上，我遇到了很多{english_profession}的专家。",
            "这款{english_app}的{english_function}设计得很{chinese_adj}。",
            "我们需要{english_action}这个{english_concept}，才能达到预期的{chinese_result}。",
            "通过{english_method}，我们可以更好的理解{chinese_topic}。",
        ]
        
        # 词汇库
        english_terms = [
            "machine learning", "artificial intelligence", "deep learning", "data science",
            "blockchain", "cloud computing", "big data", "IoT", "5G", "VR", "AR",
            "programming", "software engineering", "web development", "mobile app",
            "startup", "innovation", "technology", "digital transformation"
        ]
        
        english_places = [
            "shopping mall", "coffee shop", "restaurant", "gym", "library",
            "conference center", "co-working space", "tech hub", "innovation lab"
        ]
        
        english_activities = [
            "movie", "concert", "workshop", "meetup", "conference", "presentation",
            "demo", "hackathon", "networking event", "training session"
        ]
        
        chinese_adjectives = ["很棒", "不错", "优秀", "出色", "完美", "实用", "高效", "创新"]
        
        texts = []
        
        for _ in range(num_samples):
            template = random.choice(templates)
            
            # 随机填充模板
            text = template.format(
                english_term=random.choice(english_terms),
                english_field=random.choice(english_terms),
                english_place=random.choice(english_places),
                english_activity=random.choice(english_activities),
                english_product=random.choice(["app", "software", "platform", "tool", "system"]),
                english_feature=random.choice(["interface", "design", "performance", "functionality"]),
                english_tech=random.choice(["AI", "ML", "cloud", "automation", "analytics"]),
                english_process=random.choice(["workflow", "pipeline", "process", "operation"]),
                english_event=random.choice(["conference", "summit", "meetup", "workshop"]),
                english_profession=random.choice(["engineer", "developer", "designer", "analyst"]),
                english_app=random.choice(["application", "platform", "tool", "software"]),
                english_function=random.choice(["feature", "function", "capability", "option"]),
                english_action=random.choice(["optimize", "implement", "develop", "analyze"]),
                english_concept=random.choice(["concept", "idea", "approach", "strategy"]),
                english_method=random.choice(["method", "approach", "technique", "framework"]),
                chinese_adj=random.choice(chinese_adjectives),
                chinese_result=random.choice(["目标", "效果", "结果", "成果"]),
                chinese_topic=random.choice(["这个问题", "相关概念", "核心原理", "基本逻辑"])
            )
            
            texts.append(text)
        
        return texts

    def generate_audio_with_openai(self, text: str, voice: str = "alloy") -> bytes:
        """使用OpenAI TTS生成音频"""
        try:
            client = openai.OpenAI()
            response = client.audio.speech.create(
                model="tts-1-hd",  # 使用高质量模型
                voice=voice,
                input=text,
                response_format="wav"
            )
            return response.content
        except Exception as e:
            print(f"OpenAI TTS生成失败: {e}")
            return None

    def generate_audio_with_azure(self, text: str, voice: str = "zh-CN-XiaoxiaoNeural") -> bytes:
        """使用Azure Speech生成音频（需要配置Azure密钥）"""
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            # 配置Azure Speech服务
            speech_config = speechsdk.SpeechConfig(
                subscription=os.getenv("AZURE_SPEECH_KEY"),
                region=os.getenv("AZURE_SPEECH_REGION")
            )
            speech_config.speech_synthesis_voice_name = voice
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Wav44khz16BitMonoPcm
            )
            
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config)
            result = synthesizer.speak_text_async(text).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return result.audio_data
            else:
                print(f"Azure TTS生成失败: {result.reason}")
                return None
                
        except Exception as e:
            print(f"Azure TTS生成失败: {e}")
            return None
        
    def generate_audio_with_f5tts(self, text: str, voice: str = None) -> bytes:
        """使用F5TTS生成音频（需要配置F5TTS密钥）"""

        pass

    def process_audio(self, audio_data: bytes, target_sr: int = 44100) -> tuple:
        """处理音频数据，确保格式符合MeloTTS要求"""
        try:
            # 将字节数据转换为音频数组
            import io
            audio_buffer = io.BytesIO(audio_data)
            audio, sr = librosa.load(audio_buffer, sr=target_sr, mono=True)
            
            # 标准化音频
            audio = librosa.util.normalize(audio)
            
            return audio, sr
        except Exception as e:
            print(f"音频处理失败: {e}")
            return None, None

    def generate_dataset(self, 
                        num_samples: int = 100,
                        speaker_name: str = "cloned_speaker",
                        tts_provider: str = "openai",
                        voice: str = "alloy"):
        """生成完整的训练数据集"""
        
        print(f"开始生成 {num_samples} 条中英文混合语料...")
        
        # 生成文本
        texts = self.generate_mixed_texts(num_samples)
        
        # 生成音频和元数据
        metadata_lines = []
        
        for i, text in enumerate(tqdm(texts, desc="生成音频")):
            try:
                # 生成音频
                if tts_provider == "openai":
                    audio_data = self.generate_audio_with_openai(text, voice)
                    # audio, sr = self.process_audio(audio_data)
                elif tts_provider == "azure":
                    audio_data = self.generate_audio_with_azure(text, voice)
                    # audio, sr = self.process_audio(audio_data)
                elif tts_provider == "f5tts":
                    # print("使用F5TTS生成音频")
                    format = 'wav'
                    data = {
                        "text":text,
                        "format":format
                    } 
                    response = requests.post(self.f5tts_url, data=data)
                    if response.status_code == 200:
                        #判断返回数据的类型，audio/wav 或者 json
                        content_type = response.headers.get("Content-Type")
                        if content_type == f"audio/{format}":
                            audio_data = response.content
                            # if audio_data:
                            #     file_path = f"audio.{format}"  # Replace with the desired file path
                            #     with open(file_path, "wb") as file:
                            #         file.write(audio_data)
                    else:
                        print(f"Request failed with status code {response.status_code}")
                else:
                    raise ValueError(f"不支持的TTS提供商: {tts_provider}")
                
                # if audio_data is None:
                #     continue
                audio, sr = self.process_audio(audio_data)
        
                # if audio is None:
                #     continue
                
                # 保存音频文件
                audio_filename = f"{i:06d}.wav"
                audio_path = self.audio_dir / audio_filename
                sf.write(audio_path, audio, sr)
                
                # 添加到元数据
                relative_path = f"wavs/{audio_filename}"
                metadata_line = f"{relative_path}|{speaker_name}|ZH_MIX_EN|{text}"
                metadata_lines.append(metadata_line)
                
                # 避免API限制
                time.sleep(0.1)
                
            except Exception as e:
                print(f"处理第 {i} 条数据时出错: {e}")
                continue
        
        # 保存元数据文件
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            for line in metadata_lines:
                f.write(line + '\n')
        
        print(f"数据集生成完成！")
        print(f"- 音频文件: {len(metadata_lines)} 个")
        print(f"- 元数据文件: {self.metadata_file}")
        print(f"- 音频目录: {self.audio_dir}")
        
        return len(metadata_lines)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='生成中英文混合语音数据集')
    parser.add_argument('--num_samples', type=int, default=10, help='生成样本数量')
    parser.add_argument('--output_dir', type=str, default='generated_dataset', help='输出目录')
    parser.add_argument('--speaker_name', type=str, default='yyz', help='说话人名称')
    parser.add_argument('--tts_provider', type=str, choices=['openai', 'azure','f5tts'], 
                       default='f5tts', help='TTS服务提供商')
    parser.add_argument('--voice', type=str, default='alloy', help='语音模型')
    
    args = parser.parse_args()
    
    # 检查API密钥
    if args.tts_provider == 'openai' and not os.getenv('OPENAI_API_KEY'):
        print("请设置 OPENAI_API_KEY 环境变量")
        return
    
    if args.tts_provider == 'azure':
        if not os.getenv('AZURE_SPEECH_KEY') or not os.getenv('AZURE_SPEECH_REGION'):
            print("请设置 AZURE_SPEECH_KEY 和 AZURE_SPEECH_REGION 环境变量")
            return
    
    # 生成数据集
    generator = MixedDatasetGenerator(args.output_dir)
    generator.generate_dataset(
        num_samples=args.num_samples,
        speaker_name=args.speaker_name,
        tts_provider=args.tts_provider,
        voice=args.voice
    )

if __name__ == "__main__":
    main()
