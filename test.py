from melo.api import TTS
import time
import torch
# 语速
speed = 0.9
# 如果确定电脑有GPU支持cuda可直接写0
# 确定电脑没有GPU支持cuda直接写cpu
# 不确定有没有写auto，交给程序自行判断
device = 'cuda:0' # or cuda:0

# text = "10月22日，我国首个国产移动操作系统——华为原生鸿蒙操作系统正式发布，这也是继苹果iOS和安卓系统后，全球第三大移动操作系统。据介绍，此前已经发布过的鸿蒙系统，由于系统底座仍使用了部分AOSP开放源代码，而不得不兼容部分安卓应用软件。而此次发布的原生鸿蒙，实现了系统底座的全部自研，系统的流畅度、性能、安全特性等提升显著，也实现了国产操作系统的自主可控。"
text = """各位来宾，大家好，我是讲解员AI之眼，2025年七月3日，nice，很高兴为大家讲解。现在我们已经进入了红军长征湘江战役纪念园的区域，
这个纪念园建在湘江战役脚山铺阻击战的战场遗址上。我们行驶的这条公路是国道桂黄公路，它的左边是先锋岭，右边是米花山，先锋岭和米花山都是湘江战役的主战场，
湘江就位于我们正前方3公里的地方。大家看路边这一排排青翠的刚竹，它们象征着红军战士刚强坚韧、百折不挠的品质。"""
model = TTS(language='ZH_MIX_EN', device=device, config_path='ckpts/MeloTTS-Chinese/config.json', ckpt_path='ckpts/MeloTTS-Chinese/checkpoint.pth')
speaker_ids = model.hps.data.spk2id

output_path = 'test.mp3'

# 第一次warm up
print('warm up...')
model.tts_to_file('test,nihao,你好！', speaker_ids['ZH'], output_path, speed=speed, quiet=True)

def print_gpu_memory():
    if torch.cuda.is_available():
        current_device = torch.cuda.current_device()
        print(f"Device: {torch.cuda.get_device_name(current_device)}")
        print(f"Memory Allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
        print(f"Max Memory Allocated: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB")
        print(f"Memory Reserved: {torch.cuda.memory_reserved() / 1024**2:.2f} MB")
    else:
        print("CUDA is not available.")

test_count = 0
while True:
    test_count += 1
    # 查看显存占用
    print_gpu_memory()
    # 推理
    start = time.time()
    model.tts_to_file(text, speaker_ids['ZH'], output_path, speed=speed, quiet=True)
    print(f'[{time.time() - start:.2f}s]')

    if test_count > 1:
        break


