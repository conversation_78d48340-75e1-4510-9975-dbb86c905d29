#!/usr/bin/env python3
"""
声音克隆训练流水线
结合大模型生成的数据和少量真实语音进行声音克隆
"""

import os
import json
import shutil
from pathlib import Path
import librosa
import soundfile as sf
from typing import List, Dict
import subprocess

class VoiceCloningPipeline:
    def __init__(self, project_dir: str = "voice_cloning_project"):
        self.project_dir = Path(project_dir)
        self.project_dir.mkdir(exist_ok=True)
        
        # 目录结构
        self.real_data_dir = self.project_dir / "real_data"
        self.synthetic_data_dir = self.project_dir / "synthetic_data"
        self.combined_data_dir = self.project_dir / "combined_data"
        self.training_dir = self.project_dir / "training"
        
        for dir_path in [self.real_data_dir, self.synthetic_data_dir, 
                        self.combined_data_dir, self.training_dir]:
            dir_path.mkdir(exist_ok=True)

    def prepare_real_voice_data(self, 
                               audio_files: List[str], 
                               texts: List[str],
                               speaker_name: str = "target_speaker"):
        """准备真实语音数据"""
        
        print("准备真实语音数据...")
        
        if len(audio_files) != len(texts):
            raise ValueError("音频文件数量与文本数量不匹配")
        
        # 创建真实数据的音频目录
        real_audio_dir = self.real_data_dir / "wavs"
        real_audio_dir.mkdir(exist_ok=True)
        
        metadata_lines = []
        
        for i, (audio_file, text) in enumerate(zip(audio_files, texts)):
            # 处理音频文件
            audio, sr = librosa.load(audio_file, sr=44100, mono=True)
            audio = librosa.util.normalize(audio)
            
            # 保存处理后的音频
            output_filename = f"real_{i:04d}.wav"
            output_path = real_audio_dir / output_filename
            sf.write(output_path, audio, sr)
            
            # 添加到元数据
            relative_path = f"wavs/{output_filename}"
            metadata_line = f"{relative_path}|{speaker_name}|ZH_MIX_EN|{text}"
            metadata_lines.append(metadata_line)
        
        # 保存元数据
        metadata_file = self.real_data_dir / "metadata.list"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            for line in metadata_lines:
                f.write(line + '\n')
        
        print(f"真实语音数据准备完成: {len(metadata_lines)} 条")
        return len(metadata_lines)

    def voice_conversion_with_reference(self, 
                                      synthetic_data_dir: str,
                                      reference_audio: str,
                                      output_dir: str = None):
        """使用参考音频进行声音转换（可选步骤）"""
        
        if output_dir is None:
            output_dir = self.synthetic_data_dir / "converted"
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        print("进行声音转换以匹配目标说话人...")
        
        # 这里可以集成声音转换模型，如：
        # - RVC (Retrieval-based Voice Conversion)
        # - SoVITS
        # - OpenVoice
        
        # 示例：使用OpenVoice进行声音转换
        try:
            # 假设已安装OpenVoice
            synthetic_audio_dir = Path(synthetic_data_dir) / "wavs"
            
            for audio_file in synthetic_audio_dir.glob("*.wav"):
                output_file = output_dir / audio_file.name
                
                # 这里调用声音转换API或模型
                # 示例命令（需要根据实际模型调整）
                cmd = [
                    "python", "voice_conversion_script.py",
                    "--source", str(audio_file),
                    "--reference", reference_audio,
                    "--output", str(output_file)
                ]
                
                # subprocess.run(cmd, check=True)
                
                # 临时：直接复制文件（实际应用中替换为真实的声音转换）
                shutil.copy2(audio_file, output_file)
            
            print("声音转换完成")
            return True
            
        except Exception as e:
            print(f"声音转换失败: {e}")
            print("跳过声音转换步骤，直接使用合成语音")
            return False

    def combine_datasets(self, 
                        real_weight: float = 0.3,
                        synthetic_weight: float = 0.7,
                        speaker_name: str = "target_speaker"):
        """合并真实数据和合成数据"""
        
        print("合并数据集...")
        
        # 创建合并数据的目录
        combined_audio_dir = self.combined_data_dir / "wavs"
        combined_audio_dir.mkdir(exist_ok=True)
        
        combined_metadata = []
        file_counter = 0
        
        # 处理真实数据
        real_metadata_file = self.real_data_dir / "metadata.list"
        if real_metadata_file.exists():
            with open(real_metadata_file, 'r', encoding='utf-8') as f:
                real_lines = f.readlines()
            
            # 根据权重决定使用多少真实数据
            num_real = int(len(real_lines) * real_weight / (real_weight + synthetic_weight) * 1000)
            
            for i, line in enumerate(real_lines[:num_real]):
                parts = line.strip().split('|')
                if len(parts) >= 4:
                    old_path, spk, lang, text = parts[0], parts[1], parts[2], '|'.join(parts[3:])
                    
                    # 复制音频文件
                    old_audio_path = self.real_data_dir / old_path
                    new_filename = f"combined_{file_counter:06d}.wav"
                    new_audio_path = combined_audio_dir / new_filename
                    
                    if old_audio_path.exists():
                        shutil.copy2(old_audio_path, new_audio_path)
                        
                        # 更新元数据
                        new_line = f"wavs/{new_filename}|{speaker_name}|{lang}|{text}"
                        combined_metadata.append(new_line)
                        file_counter += 1
        
        # 处理合成数据
        synthetic_metadata_file = self.synthetic_data_dir / "metadata.list"
        if synthetic_metadata_file.exists():
            with open(synthetic_metadata_file, 'r', encoding='utf-8') as f:
                synthetic_lines = f.readlines()
            
            # 根据权重决定使用多少合成数据
            num_synthetic = int(len(synthetic_lines) * synthetic_weight / (real_weight + synthetic_weight) * 1000)
            
            for i, line in enumerate(synthetic_lines[:num_synthetic]):
                parts = line.strip().split('|')
                if len(parts) >= 4:
                    old_path, spk, lang, text = parts[0], parts[1], parts[2], '|'.join(parts[3:])
                    
                    # 复制音频文件
                    old_audio_path = self.synthetic_data_dir / old_path
                    new_filename = f"combined_{file_counter:06d}.wav"
                    new_audio_path = combined_audio_dir / new_filename
                    
                    if old_audio_path.exists():
                        shutil.copy2(old_audio_path, new_audio_path)
                        
                        # 更新元数据
                        new_line = f"wavs/{new_filename}|{speaker_name}|{lang}|{text}"
                        combined_metadata.append(new_line)
                        file_counter += 1
        
        # 保存合并后的元数据
        combined_metadata_file = self.combined_data_dir / "metadata.list"
        with open(combined_metadata_file, 'w', encoding='utf-8') as f:
            for line in combined_metadata:
                f.write(line + '\n')
        
        print(f"数据集合并完成: {len(combined_metadata)} 条")
        return len(combined_metadata)

    def prepare_training_config(self, speaker_name: str = "target_speaker"):
        """准备训练配置"""
        
        print("准备训练配置...")
        
        # 复制元数据文件到训练目录
        source_metadata = self.combined_data_dir / "metadata.list"
        target_metadata = self.training_dir / "metadata.list"
        shutil.copy2(source_metadata, target_metadata)
        
        # 创建软链接到音频文件（节省空间）
        training_audio_dir = self.training_dir / "wavs"
        # if training_audio_dir.exists():
        #     shutil.rmtree(training_audio_dir)
        if os.path.islink(training_audio_dir):
            # 是符号链接，先删除它本身
            os.unlink(training_audio_dir)
        else:
            # 是普通目录，正常删除
            shutil.rmtree(training_audio_dir)
        
        # 创建软链接
        combined_audio_dir = self.combined_data_dir / "wavs"
        training_audio_dir.symlink_to(combined_audio_dir.absolute())
        
        print("训练数据准备完成")

    def start_training(self, 
                      config_template: str = "../melo/configs/config.json",
                      num_gpus: int = 1):
        """开始训练"""
        
        print("开始MeloTTS训练...")
        
        # 切换到melo目录
        melo_dir = Path("../melo")
        if not melo_dir.exists():
            print("错误: 找不到melo目录，请确保在MeloTTS项目根目录下运行")
            return False
        
        # 运行预处理
        metadata_path = self.training_dir / "metadata.list"
        preprocess_cmd = [
            "python", "preprocess_text.py",
            "--metadata", str(metadata_path.absolute())
        ]
        
        try:
            subprocess.run(preprocess_cmd, cwd=melo_dir, check=True)
            print("文本预处理完成")
        except subprocess.CalledProcessError as e:
            print(f"预处理失败: {e}")
            return False
        
        # 开始训练
        config_path = self.training_dir / "config.json"
        train_cmd = ["bash", "train.sh", str(config_path.absolute()), str(num_gpus)]
        
        try:
            print(f"开始训练，配置文件: {config_path}")
            print("训练命令:", " ".join(train_cmd))
            print("注意: 训练可能需要数小时到数天时间")
            
            # 在后台启动训练
            subprocess.Popen(train_cmd, cwd=melo_dir)
            print("训练已在后台启动")
            return True
            
        except Exception as e:
            print(f"启动训练失败: {e}")
            return False

def main():
    """主函数 - 完整的声音克隆流水线"""
    
    pipeline = VoiceCloningPipeline("../my_voice_clone")
    
    print("=== 声音克隆训练流水线 ===")
    
    # 步骤1: 准备真实语音数据（用户需要提供）
    print("\n步骤1: 准备真实语音数据")
    print("请准备目标说话人的音频文件和对应文本")
    print("建议至少10-20条高质量录音")
    
    # 示例：用户需要替换为实际的音频文件和文本
    real_audio_files = [
        # "path/to/real_audio_1.wav",
        # "path/to/real_audio_2.wav",
        # ...
    ]
    real_texts = [
        # "对应的文本内容1",
        # "对应的文本内容2", 
        # ...
    ]
    
    if real_audio_files and real_texts:
        pipeline.prepare_real_voice_data(real_audio_files, real_texts)
    else:
        print("跳过真实数据准备（请手动准备数据）")
    
    # 步骤2: 生成合成数据（假设已经运行了generate_mixed_dataset.py）
    print("\n步骤2: 检查合成数据")
    synthetic_data_path = "../generated_dataset"
    if Path(synthetic_data_path).exists():
        # 复制合成数据到项目目录
        shutil.copytree(synthetic_data_path, pipeline.synthetic_data_dir, dirs_exist_ok=True)
        print("合成数据已准备")
    else:
        print("请先运行 generate_mixed_dataset.py 生成合成数据")
        return
    
    # 步骤3: 合并数据集
    print("\n步骤3: 合并数据集")
    total_samples = pipeline.combine_datasets(real_weight=0.2, synthetic_weight=0.8)
    
    # 步骤4: 准备训练
    print("\n步骤4: 准备训练配置")
    pipeline.prepare_training_config()
    
    # 步骤5: 开始训练
    print("\n步骤5: 开始训练")
    success = pipeline.start_training(num_gpus=1)
    
    if success:
        print("\n=== 训练已启动 ===")
        print("请监控训练进度，通常需要数小时到数天")
        print("训练完成后，模型将保存在 melo/logs/ 目录下")
    else:
        print("\n=== 训练启动失败 ===")
        print("请检查错误信息并手动启动训练")

if __name__ == "__main__":
    main()
